import React, { useState, useRef, useEffect, useCallback } from 'react';
import styles from './TextEditor.module.css';
import { CommentModal } from './CommentModal';
import { EditCommentModal } from './EditCommentModal';
import { ColorPicker } from './ColorPicker';
import TableModal from './TableModal';
import { ConfirmDialog } from '../Modal/ConfirmDialog';
import { HiBold, HiListBullet } from 'react-icons/hi2';
import { HiPencil, HiTrash, HiLink, HiTable, HiColorSwatch, HiOutlineChatAlt, HiReply, HiOutlineSave } from 'react-icons/hi';
import { Button } from '../Buttons/Button';
import { useSheetComments } from './useSheetComments';
import { SheetCommentDto, FlowStatus, CommentOperationDto, SheetUpdateWithCommentsDto, CommentOperationType, SheetCommentCreateDto, CreateCommentWithContentUpdateRequest, DeleteCommentWithContentUpdateRequest } from '../../api/proxy';
import { SheetService } from '../../api/services/SheetService';
import { useUserData } from '../../auth/AuthContext';

// Funzione per generare UUID v4
const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Props per il TextEditor
interface TextEditorProps {
  sheetId?: string;
  flowStatus?: FlowStatus;
  refId?: string;
  initialContent?: string;
  canEditContent: boolean;
  canComment: boolean;
}

// Interfaccia per rappresentare un commento locale (per compatibilità con l'UI esistente)
interface Comment {
  id: string;
  text: string;
  selection: {
    content: string;
  };
  refId: string; // UUID che identifica lo span nel contenuto HTML
  author: string;
  authorId?: string; // Add authorId to track comment creator
  createdAt: Date;
  replies?: Reply[];
}

// Interfaccia per rappresentare una risposta a un commento
interface Reply {
  id: string;
  text: string;
  author: string;
  authorId?: string; // Add authorId to track reply creator
  createdAt: Date;
}

export const TextEditor: React.FC<TextEditorProps> = ({ 
  sheetId, 
  flowStatus = FlowStatus._0, 
  canEditContent,
  canComment,
  initialContent // Nuovo prop
}) => {
  // Hook per ottenere le informazioni dell'utente corrente
  const { displayName, user } = useUserData();

  // Hook per la gestione dei commenti dal backend
  const {
    comments: backendComments,
    loading: commentsLoading,
    error: commentsError,
    createComment,
    updateComment,
    deleteComment,
    loadComments,
    clearError
  } = useSheetComments({ 
    sheetId: sheetId || 'default-sheet', 
    flowStatus 
  });

  const editorRef = useRef<HTMLDivElement>(null);

  // Funzione per convertire SheetCommentDto in Comment locale
  const convertToLocalComment = (sheetComment: SheetCommentDto): Comment => {
    // Trova lo span corrispondente nel contenuto per ottenere il testo commentato
    let selectionContent = '';
    if (editorRef.current && sheetComment.refId) {
      const span = editorRef.current.querySelector(`span[data-ref-id="${sheetComment.refId}"]`);
      if (span) {
        selectionContent = span.textContent || '';
      }
    }

    return {
      id: sheetComment.id || '',
      text: sheetComment.comment || '',
      selection: {
        content: selectionContent
      },
      refId: sheetComment.refId || '', // Il refId ora è l'UUID dello span
      author: sheetComment.creatorName || '',
      authorId: sheetComment.creatorId, // Store the creator ID
      createdAt: sheetComment.creationTime ? new Date(sheetComment.creationTime) : new Date(),
      replies: sheetComment.replies?.map(reply => ({
        id: reply.id || '',
        text: reply.comment || '',
        author: reply.creatorName || '',
        authorId: reply.creatorId, // Store the creator ID
        createdAt: reply.creationTime ? new Date(reply.creationTime) : new Date()
      })) || []
    };
  };

  // Converte i commenti del backend nel formato locale
  const comments = backendComments.map(convertToLocalComment);

  // Funzione per rimuovere evidenziazioni di commenti non più visibili
  const removeInvisibleHighlights = useCallback(() => {
    if (!editorRef.current) return;

    const highlightedSpans = editorRef.current.querySelectorAll(`.${styles.highlighted}`);
    const visibleRefIds = new Set(backendComments.map(comment => comment.refId));

    highlightedSpans.forEach(span => {
      const refId = span.getAttribute('data-ref-id');
      if (refId && !visibleRefIds.has(refId)) {
        // Questo span si riferisce a un commento non più visibile, rimuovilo
        if (span.parentNode) {
          const textContent = span.textContent || '';
          const textNode = document.createTextNode(textContent);
          span.parentNode.replaceChild(textNode, span);
          console.log(`Rimossa evidenziazione per commento non visibile con refId: ${refId}`);
        }
      }
    });
  }, [backendComments]);

  // Effetto per rimuovere evidenziazioni non più visibili quando cambiano i commenti backend
  useEffect(() => {
    removeInvisibleHighlights();
  }, [removeInvisibleHighlights]);

  // Stati dell'UI esistenti (mantenuti per compatibilità)
  const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);
  const [isTableModalOpen, setIsTableModalOpen] = useState(false);
  const [currentSelection, setCurrentSelection] = useState<{
    content: string;
  } | null>(null);
  const [commentToEdit, setCommentToEdit] = useState<Comment | null>(null);

  // Stato per la posizione dell'icona del commento
  const [commentIconPosition, setCommentIconPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);

  // Stati per tenere traccia della formattazione corrente
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isList, setIsList] = useState(false);
  const [currentFontSize, setCurrentFontSize] = useState('3'); // Valore predefinito: normale

  // Stato per tenere traccia del commento attualmente evidenziato
  const [highlightedCommentId, setHighlightedCommentId] = useState<string | null>(null);

  // Stato per gestire le risposte ai commenti
  const [replyToCommentId, setReplyToCommentId] = useState<string | null>(null);
  const [replyText, setReplyText] = useState<string>('');

  // Stati per gestire la modifica delle risposte
  const [replyToEdit, setReplyToEdit] = useState<{commentId: string, replyId: string} | null>(null);
  const [editReplyText, setEditReplyText] = useState<string>('');

  // Stati per gestire i timer di hover
  const [textHoverTimer, setTextHoverTimer] = useState<ReturnType<typeof setTimeout> | null>(null);
  const [commentHoverTimer, setCommentHoverTimer] = useState<ReturnType<typeof setTimeout> | null>(null);

  // Stato per il ridimensionamento
  const [commentsWidth, setCommentsWidth] = useState<number>(280);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const resizeHandleRef = useRef<HTMLDivElement>(null);
  const commentsListRef = useRef<HTMLDivElement>(null);

  // Stati per il placeholder
  const [isEditorEmpty, setIsEditorEmpty] = useState<boolean>(true);
  const [isEditorFocused, setIsEditorFocused] = useState<boolean>(false);

  // Stati per i commenti pendenti (quando canEditContent = true)
  const [pendingComments, setPendingComments] = useState<Comment[]>([]);
  const [pendingCommentActions, setPendingCommentActions] = useState<{
    type: 'create' | 'update' | 'delete';
    commentId?: string;
    data?: any;
  }[]>([]);

  // Stati per le risposte pendenti
  const [pendingReplies, setPendingReplies] = useState<{[commentId: string]: Reply[]}>({});
  const [pendingReplyActions, setPendingReplyActions] = useState<{
    type: 'create' | 'update' | 'delete';
    replyId?: string;
    parentCommentId?: string;
    data?: any;
  }[]>([]);

  // Nuovo stato per tracciare i commenti con azioni di delete pendenti
  const [commentsWithPendingDelete, setCommentsWithPendingDelete] = useState<Set<string>>(new Set());

  // Nuovo stato per tracciare le risposte con azioni di delete pendenti  
  const [repliesWithPendingDelete, setRepliesWithPendingDelete] = useState<Set<string>>(new Set());

  // Se canEditContent è true, mostra anche i commenti pendenti
  // ma esclude i commenti che hanno un'azione di delete pendente
  const allComments = canEditContent 
    ? [...comments, ...pendingComments].filter(comment => !commentsWithPendingDelete.has(comment.id))
    : comments;

  const containerRef = useRef<HTMLDivElement>(null);
  const selectionRef = useRef<Range | null>(null);
  const replyTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Flag per disabilitare l'auto-eliminazione durante l'evidenziazione
  const [isHighlighting, setIsHighlighting] = useState<boolean>(false);

  // Nuovo stato per tracciare se il contenuto è stato caricato
  const [contentLoaded, setContentLoaded] = useState<boolean>(false);

  // Stato per tracciare il contenuto originale dello sheet
  const [originalContent, setOriginalContent] = useState<string>('');

  // Stato per il modal di conferma annullamento
  const [showCancelConfirm, setShowCancelConfirm] = useState<boolean>(false);

  // Stato per tracciare se il contenuto dello sheet è stato modificato
  const [hasContentChanges, setHasContentChanges] = useState<boolean>(false);

  // Funzione per cancellare la selezione corrente
  const clearSelection = () => {
    setCurrentSelection(null);
    setCommentIconPosition(null);
    selectionRef.current = null;
  };

  // Funzione per gestire l'evento di copia
  const handleCopy = (e: ClipboardEvent) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Otteniamo il testo puro dalla selezione
    const text = selection.toString();

    // Impostiamo il testo puro nella clipboard
    e.clipboardData?.setData('text/plain', text);

    // Preveniamo il comportamento di default
    e.preventDefault();
  };

  // Funzione per rilevare la formattazione corrente
  const checkCurrentFormatting = () => {
    // Controlla se il testo è in grassetto
    setIsBold(document.queryCommandState('bold'));

    // Controlla se il testo è in corsivo
    setIsItalic(document.queryCommandState('italic'));

    // Controlla se il testo è sottolineato
    setIsUnderline(document.queryCommandState('underline'));

    // Controlla se il testo è in una lista
    setIsList(document.queryCommandState('insertUnorderedList'));

    // Controlla la dimensione del font
    // Nota: questo è più complesso perché document.queryCommandValue('fontSize')
    // potrebbe non restituire esattamente il valore che ci aspettiamo
    const fontSize = document.queryCommandValue('fontSize');
    if (fontSize) {
      setCurrentFontSize(fontSize);
    }
  };

  // Aggiungiamo un event listener per gestire i click fuori dall'editor
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Se il click è fuori dal container dell'editor e non è sul modal
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        !isCommentModalOpen
      ) {
        clearSelection();
      }
    };

    // Aggiungiamo l'event listener al documento
    document.addEventListener('mousedown', handleClickOutside);

    // Puliamo l'event listener quando il componente viene smontato
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isCommentModalOpen]);

  // Nuovo useEffect per caricare il contenuto iniziale
  useEffect(() => {
    if (!editorRef.current) return;

    const loadContent = async () => {
      try {
        let content = '';

        // Se abbiamo un initialContent, usalo solo se è una stringa valida
        if (initialContent && typeof initialContent === 'string' && initialContent.trim() !== '') {
          content = initialContent;
        } 
        // Altrimenti, se abbiamo uno sheetId, carica il contenuto dal server
        else if (sheetId && sheetId !== 'default-sheet') {
          try {
            const serverContent = await SheetService.getContent(sheetId);
            if (serverContent && typeof serverContent === 'string' && serverContent.trim() !== '') {
              content = serverContent;
            }
          } catch (error) {
            console.error('Error loading sheet content:', error);
            content = ''; // Usa contenuto vuoto in caso di errore
          }
        }

        // Salva il contenuto originale
        setOriginalContent(content);

        // Imposta il contenuto nell'editor
        if (content && content.trim() !== '') {
          if (editorRef.current) {
            editorRef.current.innerHTML = content;
            setIsEditorEmpty(false);
          }
        } else {
          if (editorRef.current) {
            editorRef.current.innerHTML = '';
            setIsEditorEmpty(true);
          }
        }

        // Inizializza lo stato delle modifiche (nessuna modifica al caricamento)
        setHasContentChanges(false);

        setContentLoaded(true);
      } catch (error) {
        console.error('Error in loadContent:', error);
        setContentLoaded(true);
      }
    };

    // Carica il contenuto solo se non è ancora stato caricato
    if (!contentLoaded) {
      loadContent();
    }
  }, [sheetId, initialContent, contentLoaded]);

  // Funzione per eseguire comandi di formattazione
  const execCommand = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
      // Aggiorna lo stato della formattazione dopo l'esecuzione del comando
      checkCurrentFormatting();
    }
  };

  // Funzione per inserire un link
  const insertLink = () => {
    const url = prompt('Inserisci l\'URL del link:', 'https://');
    if (url) {
      execCommand('createLink', url);
    }
  };

  // Funzione per aprire il modale della tabella
  const openTableModal = () => {
    setIsTableModalOpen(true);
  };

  // Funzione per inserire una tabella
  const insertTable = (rows: number, cols: number, hasBorder: boolean) => {
    // Assicuriamoci che i valori siano validi
    const validRows = Math.max(1, Math.min(20, rows));
    const validCols = Math.max(1, Math.min(10, cols));

    // Creiamo l'HTML della tabella
    let tableHTML = `<table ${hasBorder === true ? 'border="1"' : ''} style="width:100%; ${hasBorder === false ? 'border-collapse: collapse;' : ''}">`;

    for (let i = 0; i < validRows; i++) {
      tableHTML += '<tr>';
      for (let j = 0; j < validCols; j++) {
        tableHTML += `<td ${hasBorder === false ? 'style="border: none;"' : ''}>Cella ${i+1},${j+1}</td>`;
      }
      tableHTML += '</tr>';
    }

    tableHTML += '</table>';

    // Inseriamo la tabella nell'editor
    execCommand('insertHTML', tableHTML);
  };

  // Funzione per aprire il color picker
  const openColorPicker = () => {
    // Apri semplicemente il color picker senza preoccuparti della posizione
    setIsColorPickerOpen(true);
  };

  // Funzione per cambiare il colore del testo
  const changeTextColor = (color: string) => {
    execCommand('foreColor', color);
  };

  // Funzione per cambiare la dimensione del font
  const changeFontSize = (size: string) => {
    execCommand('fontSize', size);
    // Aggiorna lo stato della dimensione del testo
    setCurrentFontSize(size);
  };

  // Le funzioni findNthOccurrence e countOccurrencesBefore sono state rimosse
  // poiché non sono più necessarie per il funzionamento del componente

  // Gestisce la selezione del testo
  const handleTextSelection = () => {
    // Utilizziamo setTimeout per dare tempo al browser di completare la selezione
    setTimeout(() => {
      const selection = window.getSelection();
      if (selection && selection.toString().length > 0 && editorRef.current) {
        // Ottieni il range della selezione
        const range = selection.getRangeAt(0);
        const selectionContent = selection.toString();

        // Verifichiamo che la selezione sia all'interno dell'editor
        let isSelectionInEditor = false;
        let node: Node | null = range.commonAncestorContainer;
        while (node != null) {
          if (node === editorRef.current) {
            isSelectionInEditor = true;
            break;
          }
          node = node.parentNode as Node | null;
        }

        // Procediamo solo se la selezione è all'interno dell'editor
        if (isSelectionInEditor) {
          // Salviamo il range per usarlo più tardi
          selectionRef.current = range.cloneRange();

          // Salva la selezione corrente (senza più la posizione dell'occorrenza)
          setCurrentSelection({
            content: selectionContent
          });

          // Calcola la posizione dell'icona del commento
          const selectionRect = range.getBoundingClientRect();
          const editorRect = editorRef.current.getBoundingClientRect();

          // Posizionamento dell'icona accanto alla selezione, tenendo conto della toolbar
          // Otteniamo la posizione del contenitore principale
          const containerRect = containerRef.current?.getBoundingClientRect();

          // Calcoliamo la posizione relativamente al contenitore principale
          const topPosition = selectionRect.top - (containerRect?.top || 0);

          // Posizione orizzontale: appena a destra della selezione
          const leftPosition = selectionRect.right - editorRect.left + 5;

          setCommentIconPosition({
            top: topPosition,
            left: leftPosition
          });

          console.log(`Selezionato: "${selectionContent}"`);
        }
      } else {
        // Se non c'è selezione, nascondi l'icona del commento
        setCommentIconPosition(null);
        setCurrentSelection(null);
      }
    }, 10);
  };

  // Apre il modal per aggiungere un commento
  const handleAddComment = () => {
    if (currentSelection && currentSelection.content.trim().length > 0) {
      setIsCommentModalOpen(true);
    } else {
      alert('Seleziona del testo prima di aggiungere un commento');
    }
  };

  // Modifica della funzione handleSaveComment per gestire la logica condizionale
  const handleSaveCommentWithLogic = async (commentText: string) => {
    if (!canEditContent) {
      // Scenario 2: Se non può modificare il contenuto, usa il nuovo metodo atomico
      if (commentText.trim() && currentSelection && editorRef.current && sheetId) {
        // Genera un UUID per il refId che identificherà lo span
        const spanRefId = generateUUID();

        // AGGIUNTO: Crea lo span nel contenuto HTML prima di inviare la richiesta
        // Crea un commento temporaneo per usare la funzione di evidenziazione esistente
        const tempComment: Comment = {
          id: `temp-${Date.now()}`,
          text: commentText.trim(),
          selection: currentSelection,
          refId: spanRefId,
          author: displayName || '',
          authorId: user?.id || '',
          createdAt: new Date(),
          replies: []
        };

        // Applica l'evidenziazione per creare lo span nel DOM
        highlightCommentedText(tempComment);

        // Ora ottieni il contenuto HTML aggiornato con lo span
        const sheetContent = editorRef.current.innerHTML;

        try {
          const commentDto = new SheetCommentCreateDto({
            sheetId,
            refId: spanRefId, // Usa l'UUID generato come refId
            comment: commentText.trim(),
            parentCommentId: undefined,
            flowStatus
          });

          // Usa il nuovo metodo atomico che crea il commento e aggiorna il contenuto
          const requestBody = new CreateCommentWithContentUpdateRequest({
            commentDto,
            currentContent: sheetContent
          });

          const newComment = await SheetService.createCommentWithContentUpdate(sheetId, requestBody);

          if (newComment) {
            // Il commento è stato creato e il contenuto aggiornato atomicamente
            // Ricarica il contenuto dello sheet per vedere le modifiche all'HTML con l'ID reale
            try {
              const updatedContent = await SheetService.getContent(sheetId);
              if (editorRef.current && updatedContent !== null) {
                editorRef.current.innerHTML = updatedContent;

                // Aggiorna anche il contenuto originale per il tracking delle modifiche
                setOriginalContent(updatedContent);
                setHasContentChanges(false);

                // Aggiorna lo stato dell'editor vuoto
                setIsEditorEmpty(updatedContent.trim() === '');
              }
            } catch (contentError) {
              console.error('Errore nel ricaricare il contenuto dopo creazione:', contentError);
            }

            // Ricarica i commenti per avere lo stato aggiornato
            if (loadComments) {
              await loadComments();
            }

            setIsCommentModalOpen(false);
            setCurrentSelection(null);

            console.log('Commento creato e contenuto aggiornato atomicamente');
          }
        } catch (error) {
          console.error('Errore nella creazione atomica del commento:', error);
          alert('Errore nel salvataggio del commento. Riprova.');
        }
      }
    } else {
      // Scenario 1: Se può modificare il contenuto, aggiungi il commento ai pendenti
      // Il salvataggio sarà fatto manualmente con il pulsante floppy
      if (commentText.trim() && currentSelection) {
        // Genera un UUID per il refId che identificherà lo span
        const spanRefId = generateUUID();

        const newPendingComment: Comment = {
          id: `pending-${Date.now()}`, // ID temporaneo
          text: commentText.trim(),
          selection: currentSelection,
          refId: spanRefId, // Usa l'UUID generato come refId
          author: displayName || '', // Usa displayName dell'utente corrente per i commenti pendenti
          authorId: user?.id || '', // Store the creator ID
          createdAt: new Date(),
          replies: []
        };

        // Aggiungi ai commenti pendenti
        setPendingComments(prev => [...prev, newPendingComment]);

        // Aggiungi l'azione di creazione alle azioni pendenti
        setPendingCommentActions(prev => [...prev, {
          type: 'create',
          data: {
            refId: spanRefId, // Usa l'UUID generato come refId
            comment: commentText.trim(),
            parentCommentId: undefined
          }
        }]);

        // Applica l'evidenziazione al testo selezionato
        highlightCommentedText(newPendingComment);

        setIsCommentModalOpen(false);
        setCurrentSelection(null);
      }
    }
  };

  // Modifica un commento esistente usando il backend
  const handleEditComment = async (commentId: string, newText: string) => {
    if (canEditContent && commentId.startsWith('pending-')) {
      // È un commento pendente, aggiorna localmente
      setPendingComments(prev => 
        prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, text: newText.trim() }
            : comment
        )
      );
      
      // Aggiorna anche l'azione pendente corrispondente
      setPendingCommentActions(prev => 
        prev.map(action => {
          // Trova l'azione che corrisponde a questo commento pendente
          const pendingComment = pendingComments.find(c => c.id === commentId);
          if (pendingComment && action.data?.comment === pendingComment.text) {
            return { ...action, data: { ...action.data, comment: newText.trim() } };
          }
          return action;
        })
      );
    } else if (canEditContent && !commentId.startsWith('pending-')) {
      // È un commento del backend in modalità editing, aggiungi alle azioni pendenti
      setPendingCommentActions(prev => [...prev, {
        type: 'update',
        commentId,
        data: { comment: newText.trim() }
      }]);
    } else {
      // Scenario 2: salva immediatamente
      const success = await updateComment(commentId, newText.trim());
      
      if (success) {
        // Aggiorna anche il tooltip nel testo evidenziato
        if (editorRef.current) {
          // Trova il commento per ottenere il refId
          const comment = allComments.find(c => c.id === commentId);
          if (comment) {
            const highlightedSpan = editorRef.current.querySelector(`span[data-ref-id="${comment.refId}"]`);
            if (highlightedSpan) {
              const tooltip = highlightedSpan.querySelector(`.${styles.tooltip}`);
              if (tooltip) {
                // Trova tutti i commenti che si riferiscono a questo refId
                const commentsForThisSpan = allComments.filter(c => c.refId === comment.refId);
                if (commentsForThisSpan.length > 1) {
                  tooltip.textContent = `${commentsForThisSpan.length} commenti su questo testo`;
                } else {
                  tooltip.textContent = newText;
                }
              }
            }
          }
        }
      }
    }
  };

  // Funzione per ottenere tutte le risposte (backend + pending) per un commento
  const getAllRepliesForComment = (commentId: string): Reply[] => {
    // Trova il commento nei display comments (che include le modifiche pendenti alle replies del backend)
    const comment = displayComments.find(c => c.id === commentId);
    const backendReplies = comment?.replies || [];
    
    // Aggiungi le risposte pendenti
    const pendingRepliesForComment = canEditContent ? (pendingReplies[commentId] || []) : [];
    
    // Applica le modifiche pendenti alle risposte pendenti (nel caso vengano modificate dopo la creazione)
    const modifiedPendingReplies = pendingRepliesForComment.map(reply => {
      if (!canEditContent) return reply;
      
      // Controlla se ci sono modifiche pendenti per questa risposta pendente
      const pendingUpdate = pendingReplyActions.find(action => 
        action.type === 'update' && action.replyId === reply.id
      );
      
      if (pendingUpdate && pendingUpdate.data?.comment) {
        return { ...reply, text: pendingUpdate.data.comment };
      }
      
      return reply;
    }).filter(reply => !repliesWithPendingDelete.has(reply.id)); // Filtra risposte pendenti eliminate
    
    // Combina e ritorna tutte le risposte
    return [...backendReplies, ...modifiedPendingReplies];
  };

  // Funzione per aggiungere una risposta a un commento usando il backend
  const handleAddReply = async (commentId: string) => {
    if (replyText.trim() === '') return;

    // Trova il commento padre per ottenere il refId
    const parentComment = allComments.find(c => c.id === commentId);
    if (!parentComment) return;

    // Usa il refId del commento padre (che è l'UUID dello span)
    const currentRefId = parentComment.refId;

    if (canEditContent) {
      // Scenario 1: Modalità editing - aggiungi alle risposte pendenti
      const newPendingReply: Reply = {
        id: `pending-reply-${Date.now()}`,
        text: replyText.trim(),
        author: displayName || '',
        authorId: user?.id || '', // Store the creator ID
        createdAt: new Date()
      };

      // Aggiungi alle risposte pendenti per questo commento
      setPendingReplies(prev => ({
        ...prev,
        [commentId]: [...(prev[commentId] || []), newPendingReply]
      }));

      // Aggiungi l'azione alle azioni pendenti
      setPendingReplyActions(prev => [...prev, {
        type: 'create',
        parentCommentId: commentId,
        data: {
          refId: currentRefId,
          comment: replyText.trim(),
          parentCommentId: commentId
        }
      }]);
      
      setReplyText('');
      setReplyToCommentId(null);
    } else {
      // Scenario 2: Salva immediatamente usando il metodo normale per le risposte
      // Le risposte non modificano il contenuto HTML, quindi possiamo usare il metodo normale
      const success = await createComment(currentRefId, replyText.trim(), commentId);
      
      if (success) {
        setReplyText('');
        setReplyToCommentId(null);
      }
    }
  };

  // Funzione per modificare una risposta esistente
  const handleEditReply = async (commentId: string, replyId: string, newText: string) => {
    if (canEditContent && replyId.startsWith('pending-reply-')) {
      // È una risposta pendente, aggiorna localmente
      setPendingReplies(prev => ({
        ...prev,
        [commentId]: (prev[commentId] || []).map(reply => 
          reply.id === replyId 
            ? { ...reply, text: newText.trim() }
            : reply
        )
      }));
      
      // Aggiorna anche l'azione pendente corrispondente
      setPendingReplyActions(prev => 
        prev.map(action => {
          const pendingRepliesForComment = pendingReplies[commentId] || [];
          const pendingReply = pendingRepliesForComment.find(r => r.id === replyId);
          if (pendingReply && action.data?.comment === pendingReply.text && action.parentCommentId === commentId) {
            return { ...action, data: { ...action.data, comment: newText.trim() } };
          }
          return action;
        })
      );
    } else if (canEditContent && !replyId.startsWith('pending-reply-')) {
      // È una risposta del backend in modalità editing, aggiungi alle azioni pendenti
      setPendingReplyActions(prev => [...prev, {
        type: 'update',
        replyId,
        parentCommentId: commentId,
        data: { comment: newText.trim() }
      }]);
    } else {
      // Scenario 2: salva immediatamente
      const success = await updateComment(replyId, newText.trim());
      
      if (success) {
        setReplyToEdit(null);
        setEditReplyText('');
      }
    }
    
    setReplyToEdit(null);
    setEditReplyText('');
  };

  // Funzione per eliminare una risposta
  const handleDeleteReply = async (commentId: string, replyId: string) => {
    if (canEditContent && replyId.startsWith('pending-reply-')) {
      // È una risposta pendente, rimuovi localmente
      const replyToDelete = (pendingReplies[commentId] || []).find(reply => reply.id === replyId);
      if (!replyToDelete) return;
      
      // Rimuovi dalle risposte pendenti
      setPendingReplies(prev => ({
        ...prev,
        [commentId]: (prev[commentId] || []).filter(reply => reply.id !== replyId)
      }));
      
      // Rimuovi l'azione pendente corrispondente
      setPendingReplyActions(prev => 
        prev.filter(action => 
          !(action.data?.comment === replyToDelete.text && action.parentCommentId === commentId)
        )
      );
      
      return;
    }
    
    if (canEditContent && !replyId.startsWith('pending-reply-')) {
      // È una risposta del backend in modalità editing, aggiungi alle azioni pendenti
      setPendingReplyActions(prev => [...prev, {
        type: 'delete',
        replyId,
        parentCommentId: commentId
      }]);
      
      // Nascondi la risposta dalla visualizzazione aggiungendola al set
      setRepliesWithPendingDelete(prev => new Set([...prev, replyId]));
      
      return;
    }
    
    // Scenario 2: elimina immediatamente
    const success = await deleteComment(replyId);
    
    if (success) {
      // Se la risposta era in fase di modifica, chiudi il form di modifica
      if (replyToEdit && replyToEdit.replyId === replyId) {
        setReplyToEdit(null);
        setEditReplyText('');
      }
    }
  };

  // Elimina un commento usando il backend
  const handleDeleteComment = async (commentId: string) => {
    if (canEditContent && commentId.startsWith('pending-')) {
      // È un commento pendente, rimuovi localmente
      const commentToDelete = pendingComments.find(comment => comment.id === commentId);
      if (!commentToDelete) return;
      
      // Rimuovi il commento dai pendenti
      setPendingComments(prev => prev.filter(comment => comment.id !== commentId));
      
      // Rimuovi l'azione pendente corrispondente
      setPendingCommentActions(prev => 
        prev.filter(action => 
          !(action.data?.comment === commentToDelete.text)
        )
      );
      
      // FIX 1: Rimuovi anche eventuali risposte pendenti a questo commento
      if (pendingReplies[commentId]) {
        setPendingReplies(prev => {
          const newPendingReplies = { ...prev };
          delete newPendingReplies[commentId];
          return newPendingReplies;
        });
        
        // Rimuovi anche le azioni pendenti delle risposte
        setPendingReplyActions(prev => 
          prev.filter(action => action.parentCommentId !== commentId)
        );
      }
      
      // Rimuovi l'evidenziazione dal testo
      if (editorRef.current && commentToDelete) {
        const highlightedSpan = editorRef.current.querySelector(`span[data-ref-id="${commentToDelete.refId}"]`);
        if (highlightedSpan && highlightedSpan.parentNode) {
          // Verifica se ci sono altri commenti che si riferiscono allo stesso refId
          const remainingComments = allComments.filter(c =>
            c.id !== commentId &&
            c.refId === commentToDelete.refId
          );

          if (remainingComments.length === 0) {
            // Nessun altro commento, rimuovi completamente lo span
            const originalText = commentToDelete.selection.content;
            const textNode = document.createTextNode(originalText);
            highlightedSpan.parentNode.replaceChild(textNode, highlightedSpan);
          } else {
            // Ci sono altri commenti, aggiorna solo il tooltip
            const tooltip = highlightedSpan.querySelector(`.${styles.tooltip}`);
            if (tooltip) {
              if (remainingComments.length === 1) {
                tooltip.textContent = remainingComments[0].text;
              } else {
                tooltip.textContent = `${remainingComments.length} commenti su questo testo`;
              }
            }
          }
        }
      }
      
      return;
    }
    
    // Trova il commento da eliminare (dal backend o dai pending)
    const commentToDelete = allComments.find(comment => comment.id === commentId);
    if (!commentToDelete) return;

    if (canEditContent && !commentId.startsWith('pending-')) {
      // È un commento del backend in modalità editing, aggiungi alle azioni pendenti
      setPendingCommentActions(prev => [...prev, {
        type: 'delete',
        commentId
      }]);
      
      // FIX 1: Aggiungi alle azioni pendenti anche l'eliminazione di tutte le risposte del commento
      if (commentToDelete) {
        const commentReplies = commentToDelete.replies || [];
        if (commentReplies.length > 0) {
          const replyDeleteActions = commentReplies.map(reply => ({
            type: 'delete' as const,
            replyId: reply.id,
            parentCommentId: commentId
          }));
          setPendingReplyActions(prev => [...prev, ...replyDeleteActions]);
          
          // Nascondi anche tutte le risposte dalla visualizzazione
          const replyIds = commentReplies.map(reply => reply.id);
          setRepliesWithPendingDelete(prev => new Set([...prev, ...replyIds]));
        }
      }
      
      // FIX 1: Rimuovi anche eventuali risposte pendenti a questo commento
      if (pendingReplies[commentId]) {
        setPendingReplies(prev => {
          const newPendingReplies = { ...prev };
          delete newPendingReplies[commentId];
          return newPendingReplies;
        });
        
        // Rimuovi anche le azioni pendenti delle risposte pendenti
        setPendingReplyActions(prev => 
          prev.filter(action => action.parentCommentId !== commentId)
        );
      }
      
      // FIX 2: Nascondi il commento dalla visualizzazione aggiungendolo al set
      setCommentsWithPendingDelete(prev => new Set([...prev, commentId]));
      
      // FIX 3: Rimuovi l'evidenziazione dal testo anche per i commenti del backend in modalità editing
      if (editorRef.current && commentToDelete) {
        // Verifica se ci sono altri commenti sullo stesso span (stesso refId)
        // Escludiamo i commenti che hanno delete pendente e il commento corrente
        const sameSpanComments = allComments.filter(comment =>
          comment.id !== commentId &&
          comment.refId === commentToDelete.refId &&
          !commentsWithPendingDelete.has(comment.id)
        );

        // Cerca lo span usando il refId
        const highlightedSpan = editorRef.current.querySelector(`span[data-ref-id="${commentToDelete.refId}"]`);

        if (highlightedSpan) {
          // Rimuovi l'evidenziazione dal testo solo se non ci sono altri commenti sullo stesso span
          if (sameSpanComments.length === 0) {
            if (highlightedSpan.parentNode) {
              const originalText = commentToDelete.selection.content;
              const textNode = document.createTextNode(originalText);
              highlightedSpan.parentNode.replaceChild(textNode, highlightedSpan);
            }
          } else {
            // Se ci sono altri commenti sullo stesso span, aggiorna solo il tooltip
            const tooltip = highlightedSpan.querySelector(`.${styles.tooltip}`);
            if (tooltip) {
              if (sameSpanComments.length === 1) {
                tooltip.textContent = sameSpanComments[0].text;
              } else {
                tooltip.textContent = `${sameSpanComments.length} commenti su questo testo`;
              }
            }
          }
        }
      }
      
      return;
    }

    // Scenario 2: elimina immediatamente usando il metodo atomico
    if (editorRef.current && sheetId) {
      const sheetContent = editorRef.current.innerHTML;

      try {
        const deleteRequest = new DeleteCommentWithContentUpdateRequest({
          currentContent: sheetContent
        });
        await SheetService.deleteCommentWithContentUpdate(sheetId, commentId, deleteRequest);

        // Pulisci lo stato di evidenziazione se il commento eliminato era quello evidenziato
        if (highlightedCommentId === commentId) {
          setHighlightedCommentId(null);
        }

        // Pulisci eventuali timer di hover attivi per questo commento
        if (textHoverTimer) {
          clearTimeout(textHoverTimer);
          setTextHoverTimer(null);
        }
        if (commentHoverTimer) {
          clearTimeout(commentHoverTimer);
          setCommentHoverTimer(null);
        }

        // Il commento è stato eliminato e il contenuto aggiornato atomicamente
        // Ricarica il contenuto dello sheet per vedere le modifiche all'HTML
        try {
          const updatedContent = await SheetService.getContent(sheetId);
          if (editorRef.current && updatedContent !== null) {
            editorRef.current.innerHTML = updatedContent;

            // Aggiorna anche il contenuto originale per il tracking delle modifiche
            setOriginalContent(updatedContent);
            setHasContentChanges(false);

            // Aggiorna lo stato dell'editor vuoto
            setIsEditorEmpty(updatedContent.trim() === '');
          }
        } catch (contentError) {
          console.error('Errore nel ricaricare il contenuto dopo eliminazione:', contentError);
        }

        // Ricarica i commenti per avere lo stato aggiornato
        if (loadComments) {
          await loadComments();
        }

        console.log('Commento eliminato e contenuto aggiornato atomicamente');
      } catch (error) {
        console.error('Errore nell\'eliminazione atomica del commento:', error);
        alert('Errore nell\'eliminazione del commento. Riprova.');
      }
    } else {
      console.error('Impossibile eliminare il commento: editor o sheetId non disponibili');
    }
  };

  // Evidenzia il testo commentato
  const highlightCommentedText = (comment: Comment) => {
    if (!editorRef.current) return;

    // Verifica se il commento è visibile secondo la visibility matrix
    // Solo i commenti presenti in backendComments sono visibili
    const isCommentVisible = backendComments.some(bc => bc.refId === comment.refId) ||
                            comment.id.startsWith('pending-') ||
                            comment.id.startsWith('temp-');

    if (!isCommentVisible) {
      console.log(`Commento ${comment.id} non visibile secondo visibility matrix, skip evidenziazione`);
      return;
    }

    // Imposta il flag per disabilitare l'auto-eliminazione
    setIsHighlighting(true);

    // Verifica se esiste già un'evidenziazione per lo stesso refId (stesso span)
    const existingSpan = editorRef.current.querySelector(`span[data-ref-id="${comment.refId}"]`);

    // Se esiste già uno span con lo stesso refId, aggiorna solo il tooltip
    if (existingSpan) {
      // Trova tutti i commenti che si riferiscono a questo refId
      const commentsForThisSpan = allComments.filter(c => c.refId === comment.refId);

      // Aggiorna il tooltip per mostrare che ci sono più commenti
      const tooltip = existingSpan.querySelector(`.${styles.tooltip}`);
      if (tooltip) {
        if (commentsForThisSpan.length === 1) {
          tooltip.textContent = commentsForThisSpan[0].text;
        } else {
          tooltip.textContent = `${commentsForThisSpan.length} commenti su questo testo`;
        }
      }

      // Riabilita l'auto-eliminazione dopo l'evidenziazione
      setTimeout(() => setIsHighlighting(false), 100);
      return;
    }

    // Utilizziamo il range salvato se disponibile
    if (selectionRef.current) {
      try {
        // Creiamo un nuovo span per evidenziare il testo
        const highlightSpan = document.createElement('span');
        highlightSpan.className = styles.highlighted;
        highlightSpan.setAttribute('data-ref-id', comment.refId); // Solo il refId UUID

        // Aggiungiamo gli eventi per l'evidenziazione incrociata e lo scroll
        highlightSpan.addEventListener('mouseenter', () => {
          handleTextHover(comment.id, 'enter');
        });
        highlightSpan.addEventListener('mouseleave', () => {
          handleTextHover(comment.id, 'leave');
        });

        // Aggiungiamo il tooltip con il commento
        const tooltip = document.createElement('span');
        tooltip.className = styles.tooltip;
        tooltip.textContent = comment.text;
        highlightSpan.appendChild(tooltip);

        // Estraiamo il contenuto del range e lo inseriamo nello span
        selectionRef.current.surroundContents(highlightSpan);

        // Aggiungiamo automaticamente uno spazio dopo lo span per evitare problemi di cursore
        if (!highlightSpan.nextSibling ||
            (highlightSpan.nextSibling.nodeType === Node.TEXT_NODE &&
             highlightSpan.nextSibling.textContent === '')) {
          const spaceNode = document.createTextNode('\u00A0'); // Spazio non-breaking
          highlightSpan.parentNode?.insertBefore(spaceNode, highlightSpan.nextSibling);
        }

        // Puliamo il range salvato dopo averlo utilizzato
        selectionRef.current = null;
      } catch (error) {
        console.error('Errore nell\'evidenziare il testo con range:', error);

        // Fallback: utilizziamo un metodo più semplice
        simpleFallbackHighlight(comment);
      }
    } else {
      // Se non abbiamo un range salvato, utilizziamo il metodo di fallback
      simpleFallbackHighlight(comment);
    }
    
    // Riabilita l'auto-eliminazione dopo l'evidenziazione
    setTimeout(() => setIsHighlighting(false), 100);
  };

  // Metodo di fallback semplificato
  const simpleFallbackHighlight = (comment: Comment) => {
    if (!editorRef.current) return;

    // Usa la selezione corrente se disponibile
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // Crea lo span
      const highlightSpan = document.createElement('span');
      highlightSpan.className = styles.highlighted;
      highlightSpan.setAttribute('data-ref-id', comment.refId); // Solo il refId UUID

      // Aggiungi gli event listener
      highlightSpan.addEventListener('mouseenter', () => {
        handleTextHover(comment.id, 'enter');
      });
      highlightSpan.addEventListener('mouseleave', () => {
        handleTextHover(comment.id, 'leave');
      });

      // Aggiungi il tooltip
      const tooltip = document.createElement('span');
      tooltip.className = styles.tooltip;
      tooltip.textContent = comment.text;
      highlightSpan.appendChild(tooltip);

      try {
        // Applica lo span al range
        range.surroundContents(highlightSpan);

        // Aggiungiamo automaticamente uno spazio dopo lo span per evitare problemi di cursore
        if (!highlightSpan.nextSibling ||
            (highlightSpan.nextSibling.nodeType === Node.TEXT_NODE &&
             highlightSpan.nextSibling.textContent === '')) {
          const spaceNode = document.createTextNode('\u00A0'); // Spazio non-breaking
          highlightSpan.parentNode?.insertBefore(spaceNode, highlightSpan.nextSibling);
        }
      } catch (error) {
        console.error('Errore nel metodo di fallback:', error);
        alert('Non è stato possibile evidenziare il testo selezionato. Prova a selezionare di nuovo.');
      }
    } else {
      // Se non c'è selezione, mostra un messaggio
      alert('Seleziona del testo prima di aggiungere un commento');
    }
  };

  // Effetto per gestire l'evidenziazione incrociata tra commenti nel testo e nell'elenco
  useEffect(() => {
    if (!editorRef.current) return;

    // Trova tutti gli span evidenziati nel testo
    const highlightedSpans = editorRef.current.querySelectorAll(`.${styles.highlighted}`);

    // Rimuovi la classe di evidenziazione attiva da tutti gli span
    highlightedSpans.forEach(span => {
      span.classList.remove(styles.highlightedActive);
    });

    // Se c'è un commento evidenziato, trova lo span corrispondente usando il refId del commento
    if (highlightedCommentId) {
      const comment = allComments.find(c => c.id === highlightedCommentId);
      if (comment) {
        const activeSpan = editorRef.current.querySelector(`span[data-ref-id="${comment.refId}"]`);
        if (activeSpan) {
          activeSpan.classList.add(styles.highlightedActive);
        }
      }
    }
  }, [highlightedCommentId, allComments]);

  // Effetto per monitorare la rimozione del testo evidenziato e eliminare i commenti associati
  useEffect(() => {
    if (!editorRef.current) return;

    const observer = new MutationObserver((mutations) => {
      // Non fare nulla se siamo nel processo di evidenziazione
      if (isHighlighting) return;
      
      mutations.forEach((mutation) => {
        // Controlla SOLO se sono stati rimossi dei nodi che contengono span evidenziati
        if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
          mutation.removedNodes.forEach((removedNode) => {
            // Verifica se il nodo rimosso è uno span evidenziato o lo contiene
            let spansToRemove: Element[] = [];
            
            if (removedNode.nodeType === Node.ELEMENT_NODE) {
              const element = removedNode as Element;
              
              // Se il nodo rimosso è direttamente uno span evidenziato
              if (element.classList && element.classList.contains(styles.highlighted)) {
                spansToRemove.push(element);
              }
              
              // Cerca span evidenziati all'interno del nodo rimosso
              const highlightedSpans = element.querySelectorAll(`.${styles.highlighted}`);
              spansToRemove.push(...Array.from(highlightedSpans));
            }
            
            // Elimina i commenti associati agli span rimossi SOLO se gli span sono stati effettivamente rimossi dall'editor
            spansToRemove.forEach((span) => {
              const refId = span.getAttribute('data-ref-id');

              // Verifica che lo span non sia più presente nell'editor prima di eliminare i commenti
              if (refId && !editorRef.current?.contains(span)) {
                // Trova tutti i commenti che si riferiscono a questo refId
                const commentsToDelete = allComments.filter(c => c.refId === refId);
                commentsToDelete.forEach(comment => {
                  handleAutoDeleteComment(comment.id);
                });
              }
            });
          });
        }
        
        // RIMUOVO la chiamata automatica a validateExistingHighlights() per evitare 
        // la rimozione errata dell'evidenziazione quando si digita vicino al testo evidenziato
        // La validazione ora avviene solo quando l'utente preme Delete/Backspace
      });
    });

    // Configura l'observer per monitorare SOLO le modifiche ai figli (non characterData)
    observer.observe(editorRef.current, {
      childList: true,
      subtree: true
      // Rimuovo characterData: true per evitare trigger eccessivi
    });

    // Cleanup dell'observer
    return () => {
      observer.disconnect();
    };
  }, [allComments, isHighlighting]); // Aggiunta dipendenza da isHighlighting

  // Funzione per scrollare fino al commento nella lista dei commenti
  const scrollToComment = (commentId: string) => {
    if (!commentsListRef.current) return;

    const commentElement = commentsListRef.current.querySelector(`li[data-comment-id="${commentId}"]`);
    if (commentElement) {
      commentElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  };

  // Funzione per scrollare fino al testo evidenziato nell'editor
  const scrollToHighlightedText = (commentId: string) => {
    if (!editorRef.current) return;

    // Trova il commento per ottenere il refId
    const comment = allComments.find(c => c.id === commentId);
    if (comment) {
      const highlightedSpan = editorRef.current.querySelector(`span[data-ref-id="${comment.refId}"]`);
      if (highlightedSpan) {
        highlightedSpan.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  };

  // Funzione per gestire l'hover sul testo evidenziato
  const handleTextHover = (commentId: string, action: 'enter' | 'leave') => {
    // Cancella il timer esistente se presente
    if (textHoverTimer) {
      clearTimeout(textHoverTimer);
      setTextHoverTimer(null);
    }

    if (action === 'enter') {
      // Imposta immediatamente l'evidenziazione
      setHighlightedCommentId(commentId);

      // Imposta un timer per lo scroll dopo 1 secondo
      const timer = setTimeout(() => {
        scrollToComment(commentId);
      }, 1000);

      setTextHoverTimer(timer);
    } else {
      setHighlightedCommentId(null);
    }
  };

  // Funzione per gestire l'hover sul commento
  const handleCommentHover = (commentId: string, action: 'enter' | 'leave') => {
    // Cancella il timer esistente se presente
    if (commentHoverTimer) {
      clearTimeout(commentHoverTimer);
      setCommentHoverTimer(null);
    }

    if (action === 'enter') {
      // Imposta immediatamente l'evidenziazione
      setHighlightedCommentId(commentId);

      // Imposta un timer per lo scroll dopo 1 secondo
      const timer = setTimeout(() => {
        scrollToHighlightedText(commentId);
      }, 1000);

      setCommentHoverTimer(timer);
    } else {
      setHighlightedCommentId(null);
    }
  };

  // Aggiungiamo un event listener per gestire gli eventi personalizzati di evidenziazione
  useEffect(() => {
    if (!editorRef.current) return;

    const handleHighlightComment = (event: CustomEvent) => {
      const { id, action } = event.detail;
      handleTextHover(id, action);
    };

    // Aggiungiamo l'event listener per l'evento personalizzato
    editorRef.current.addEventListener('highlight-comment', handleHighlightComment as EventListener);

    // Puliamo l'event listener quando il componente viene smontato
    return () => {
      if (editorRef.current) {
        editorRef.current.removeEventListener('highlight-comment', handleHighlightComment as EventListener);
      }

      // Puliamo anche i timer quando il componente viene smontato
      if (textHoverTimer) clearTimeout(textHoverTimer);
      if (commentHoverTimer) clearTimeout(commentHoverTimer);
    };
  }, [textHoverTimer, commentHoverTimer]);

  // Effetto per impostare il focus sulla textarea di risposta
  useEffect(() => {
    // Se replyToCommentId è impostato, significa che l'utente ha cliccato per rispondere
    if (replyToCommentId && replyTextareaRef.current) {
      // Imposta il focus sulla textarea
      replyTextareaRef.current.focus();
    }
  }, [replyToCommentId]);

  // Gestione del ridimensionamento
  useEffect(() => {
    if (!resizeHandleRef.current || !commentsListRef.current) return;

    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      setIsDragging(true);

      const startX = e.clientX;
      const startWidth = commentsListRef.current?.getBoundingClientRect().width || 280;

      const handleMouseMove = (e: MouseEvent) => {
        if (!commentsListRef.current) return;

        const containerWidth = containerRef.current?.getBoundingClientRect().width || 0;
        // Calcola la nuova larghezza sottraendo lo spostamento del mouse dalla larghezza iniziale
        // Nota: usiamo startWidth - (e.clientX - startX) perché stiamo trascinando da sinistra a destra
        const newWidth = Math.max(200, Math.min(startWidth - (e.clientX - startX), containerWidth * 0.7));

        setCommentsWidth(newWidth);
        commentsListRef.current.style.width = `${newWidth}px`;
      };

      const handleMouseUp = () => {
        setIsDragging(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    resizeHandleRef.current.addEventListener('mousedown', handleMouseDown);

    return () => {
      if (resizeHandleRef.current) {
        resizeHandleRef.current.removeEventListener('mousedown', handleMouseDown);
      }
    };
  }, []);

  // Aggiungiamo un event listener per aggiornare la formattazione quando il cursore si muove
  useEffect(() => {
    if (!editorRef.current) return;

    const handleSelectionChange = () => {
      // Verifica se la selezione è all'interno dell'editor
      const selection = window.getSelection();
      if (!selection) return;

      let node = selection.anchorNode;
      while (node && node !== editorRef.current) {
        node = node.parentNode;
      }

      // Se la selezione è all'interno dell'editor, aggiorna la formattazione
      if (node === editorRef.current) {
        checkCurrentFormatting();
      }
    };

    // Aggiungiamo l'event listener per il cambio di selezione
    document.addEventListener('selectionchange', handleSelectionChange);

    // Aggiungiamo anche un event listener per il click nell'editor
    const handleClick = () => {
      checkCurrentFormatting();
    };

    // Aggiungiamo anche un event listener per i tasti nell'editor
    const handleKeyUp = () => {
      checkCurrentFormatting();
    };

    // Aggiungiamo l'event listener per la copia
    const handleCopyEvent = (e: ClipboardEvent) => {
      // Verifica se la selezione è all'interno dell'editor
      const selection = window.getSelection();
      if (!selection) return;

      let node = selection.anchorNode;
      while (node && node !== editorRef.current) {
        node = node.parentNode;
      }

      // Se la selezione è all'interno dell'editor, gestisci la copia
      if (node === editorRef.current) {
        handleCopy(e);
      }
    };

    editorRef.current.addEventListener('click', handleClick);
    editorRef.current.addEventListener('keyup', handleKeyUp);
    editorRef.current.addEventListener('copy', handleCopyEvent as EventListener);

    // Puliamo gli event listener quando il componente viene smontato
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      if (editorRef.current) {
        editorRef.current.removeEventListener('click', handleClick);
        editorRef.current.removeEventListener('keyup', handleKeyUp);
        editorRef.current.removeEventListener('copy', handleCopyEvent as EventListener);
      }
    };
  }, []);

  // Funzioni per gestire il salvataggio dello sheet
  const handleSaveSheet = async () => {
    if (!editorRef.current || !sheetId) return;

    const sheetContent = editorRef.current.innerHTML;
    
    try {
      if (canEditContent && (pendingCommentActions.length > 0 || pendingReplyActions.length > 0)) {
        // Usa la nuova API transazionale per risolvere i problemi di parentId
        console.log('Salvando con API transazionale:', pendingCommentActions, pendingReplyActions);
        
        // Crea la mappa degli ID temporanei
        const tempIdMap = new Map<string, string>();
        
        // Aggiungi anche i commenti pendenti alla mappa temporanea
        pendingComments.forEach(comment => {
          tempIdMap.set(comment.id, comment.id);
        });
        
        // Converte le azioni pendenti nel nuovo formato
        const commentOperations: CommentOperationDto[] = [];
        
        // PRIORITÀ: Se ci sono commenti pendenti, usa quelli invece delle azioni
        // per evitare duplicazioni
        const hasPendingComments = pendingComments.length > 0 || Object.keys(pendingReplies).length > 0;
        
        if (hasPendingComments) {
          // Scenario 1: Ci sono commenti/risposte pendenti - usa questi
          console.log('Usando commenti/risposte pendenti');
          
          // Aggiungi i commenti pendenti
          pendingComments.forEach((comment, index) => {
            const commentOp = new CommentOperationDto({
              operation: CommentOperationType._1, // Create = 1
              temporaryId: comment.id,
              refId: comment.refId, // Usa il refId UUID invece del contenuto
              comment: comment.text,
              // I commenti pendenti sono parent, quindi ParentTemporaryId è vuoto ma presente
              parentTemporaryId: '',
              parentCommentId: undefined
            });
            commentOperations.push(commentOp);
            console.log(`Aggiunto commento pendente ${index}:`, commentOp);
          });
          
          // Aggiungi le risposte pendenti
          Object.entries(pendingReplies).forEach(([parentCommentId, replies]) => {
            replies.forEach((reply, replyIndex) => {
              // Determina se il parent è pending o reale
              const isParentPending = parentCommentId.startsWith('pending-');
              
              const replyOp = new CommentOperationDto({
                operation: CommentOperationType._1, // Create = 1
                temporaryId: reply.id,
                refId: '', // Le risposte non hanno refId proprio
                comment: reply.text,
                parentTemporaryId: isParentPending ? parentCommentId : '',
                parentCommentId: isParentPending ? undefined : parentCommentId
              });
              commentOperations.push(replyOp);
              console.log(`Aggiunta risposta pendente ${replyIndex} al parent ${parentCommentId}:`, replyOp);
            });
          });
        } else {
          // Scenario 2: Non ci sono commenti pendenti - usa le azioni
          console.log('Usando azioni pendenti sui commenti esistenti');
          
          // Aggiungi le azioni dai commenti esistenti
          pendingCommentActions.forEach((action, actionIndex) => {
            let operation: CommentOperationType;
            if (action.type === 'create') {
              operation = CommentOperationType._1; // Create = 1
            } else if (action.type === 'update') {
              operation = CommentOperationType._2; // Update = 2
            } else {
              operation = CommentOperationType._3; // Delete = 3
            }
            
            // Per le azioni sui commenti esistenti, il parentCommentId dovrebbe essere sempre un GUID valido o undefined
            let parentCommentId: string | undefined = action.data?.parentCommentId;
            if (parentCommentId && parentCommentId.startsWith('pending-')) {
              parentCommentId = undefined; // Non inviare parentCommentId temporanei
            }
            
            const actionOp = new CommentOperationDto({
              operation,
              commentId: action.commentId,
              // Per CREATE operations dalle azioni pendenti, generiamo un TemporaryId se manca
              temporaryId: action.type === 'create' ? `action-${Date.now()}-${Math.random()}` : undefined,
              refId: action.data?.refId,
              comment: action.data?.comment,
              parentCommentId: parentCommentId,
              // Per CREATE operations, assicurati che ParentTemporaryId sia presente anche se vuoto
              parentTemporaryId: action.type === 'create' ? '' : undefined
            });
            commentOperations.push(actionOp);
            console.log(`Aggiunta azione commento ${actionIndex} (${action.type}):`, actionOp);
          });
          
          // Aggiungi le azioni dalle risposte esistenti
          pendingReplyActions.forEach((action, replyActionIndex) => {
            let operation: CommentOperationType;
            if (action.type === 'create') {
              operation = CommentOperationType._1; // Create = 1
            } else if (action.type === 'update') {
              operation = CommentOperationType._2; // Update = 2
            } else {
              operation = CommentOperationType._3; // Delete = 3
            }
            
            // Gestisci correttamente il parentCommentId per le risposte
            let parentCommentId: string | undefined = action.data?.parentCommentId || action.parentCommentId;
            let parentTemporaryId: string | undefined = undefined;
            
            // Se il parentCommentId è "pending-", usalo come parentTemporaryId e rimuovi parentCommentId
            if (parentCommentId && parentCommentId.startsWith('pending-')) {
              parentTemporaryId = parentCommentId;
              parentCommentId = undefined;
            }
            
            const replyActionOp = new CommentOperationDto({
              operation,
              commentId: action.replyId,
              // Per CREATE operations dalle azioni pendenti, generiamo un TemporaryId se manca
              temporaryId: action.type === 'create' ? `reply-${Date.now()}-${Math.random()}` : undefined,
              refId: action.data?.refId,
              comment: action.data?.comment,
              parentCommentId: parentCommentId,
              // Assicurati che ParentTemporaryId sia sempre presente per CREATE operations
              parentTemporaryId: action.type === 'create' ? (parentTemporaryId || '') : undefined
            });
            commentOperations.push(replyActionOp);
            console.log(`Aggiunta azione risposta ${replyActionIndex} (${action.type}):`, replyActionOp);
          });
        }
        
        const updateDto = new SheetUpdateWithCommentsDto({
          content: sheetContent,
          flowStatus: flowStatus,
          commentOperations
        });
        
        // Validazione del DTO prima dell'invio
        if (!updateDto) {
          throw new Error('UpdateDto è undefined');
        }
        
        if (!updateDto.commentOperations) {
          updateDto.commentOperations = [];
        }
        
        // Debug dettagliato con validazione
        console.log('UpdateDto inviato:', updateDto);
        console.log('UpdateDto JSON:', JSON.stringify(updateDto, null, 2));
        console.log('CommentOperations count:', commentOperations.length);
        
        // Validazione e debug delle operazioni
        commentOperations.forEach((op, index) => {
          console.log(`Operation ${index}:`, {
            operation: op.operation,
            commentId: op.commentId,
            temporaryId: op.temporaryId,
            parentCommentId: op.parentCommentId,
            parentTemporaryId: op.parentTemporaryId,
            refId: op.refId,
            comment: op.comment
          });
          
          // Validazione finale: assicurati che non ci siano ID "pending-" nei campi GUID
          if (op.parentCommentId && op.parentCommentId.startsWith('pending-')) {
            console.error(`ERRORE: parentCommentId contiene valore temporaneo: ${op.parentCommentId}`);
            throw new Error(`parentCommentId non valido: ${op.parentCommentId}`);
          }
          
          if (op.commentId && op.commentId.startsWith('pending-')) {
            console.error(`ERRORE: commentId contiene valore temporaneo: ${op.commentId}`);
            throw new Error(`commentId non valido: ${op.commentId}`);
          }
        });
        
        const response = await SheetService.updateContentWithComments(sheetId, updateDto);
        
        if (response.success) {
          console.log('Salvataggio transazionale completato con successo');
          console.log('Mappa ID temporanei → reali:', response.temporaryIdMap);
          
          // Aggiorna il contenuto originale con quello appena salvato
          setOriginalContent(sheetContent);
          
          // Resetta lo stato delle modifiche al contenuto
          setHasContentChanges(false);
          
          // Gli span ora usano solo data-ref-id, non è necessario aggiornare nulla
          // Il mapping temporaryIdMap serve solo per aggiornare i commenti in memoria
          // Gli span mantengono il loro data-ref-id UUID che non cambia mai
          
          // Pulisce gli stati pendenti
          setPendingCommentActions([]);
          setPendingReplyActions([]);
          setPendingComments([]);
          setPendingReplies({});
          
          // Pulisce anche il set dei commenti con delete pendente
          setCommentsWithPendingDelete(new Set());
          
          // Pulisce anche il set delle risposte con delete pendente
          setRepliesWithPendingDelete(new Set());
          
          // Ricarica i commenti dal backend per avere lo stato aggiornato
          if (loadComments) {
            await loadComments();
          }
        } else {
          console.error('Errore nel salvataggio transazionale:', response.errorMessage);
          throw new Error(response.errorMessage || 'Errore sconosciuto nel salvataggio');
        }
      } else {
        // Salvataggio del solo contenuto utilizzando updateContentWithComments con array vuoto
        const updateDto = new SheetUpdateWithCommentsDto({
          content: sheetContent,
          flowStatus: flowStatus,
          commentOperations: [] // Array vuoto per indicare nessuna operazione sui commenti
        });
        
        const response = await SheetService.updateContentWithComments(sheetId, updateDto);
        
        if (response.success) {
          // Aggiorna il contenuto originale con quello appena salvato
          setOriginalContent(sheetContent);
          
          // Resetta lo stato delle modifiche al contenuto
          setHasContentChanges(false);
          
          console.log('Salvataggio del contenuto completato');
        } else {
          console.error('Errore nel salvataggio del contenuto:', response.errorMessage);
          throw new Error(response.errorMessage || 'Errore sconosciuto nel salvataggio');
        }
      }
    } catch (error) {
      console.error('Errore nel salvataggio dello sheet:', error);
      throw error; // Rilancia l'errore per gestirlo nell'UI
    }
  };

  // Funzione per eliminare automaticamente un commento quando il testo viene rimosso
  const handleAutoDeleteComment = async (commentId: string) => {
    console.log(`Auto-eliminazione commento: ${commentId}`);
    
    if (canEditContent && commentId.startsWith('pending-')) {
      // È un commento pendente, rimuovi localmente senza conferma
      const commentToDelete = pendingComments.find(comment => comment.id === commentId);
      if (!commentToDelete) return;
      
      setPendingComments(prev => prev.filter(comment => comment.id !== commentId));
      setPendingCommentActions(prev => 
        prev.filter(action => {
          // Filtra le azioni che si riferiscono a questo commento
          const commentToDelete = pendingComments.find(c => c.id === commentId);
          return !(commentToDelete && action.data?.comment === commentToDelete.text);
        })
      );
      
      // FIX 1: Rimuovi anche eventuali risposte pendenti a questo commento
      if (pendingReplies[commentId]) {
        setPendingReplies(prev => {
          const newPendingReplies = { ...prev };
          delete newPendingReplies[commentId];
          return newPendingReplies;
        });
        
        // Rimuovi anche le azioni pendenti delle risposte
        setPendingReplyActions(prev => 
          prev.filter(action => action.parentCommentId !== commentId)
        );
      }
    } else if (canEditContent && !commentId.startsWith('pending-')) {
      // È un commento del backend in modalità editing, aggiungi alle azioni pendenti
      
      // Trova il commento da eliminare per gestire le sue risposte
      const commentToDelete = allComments.find(comment => comment.id === commentId);
      
      setPendingCommentActions(prev => [...prev, {
        type: 'delete',
        commentId
      }]);
      
      // FIX 1: Aggiungi alle azioni pendenti anche l'eliminazione di tutte le risposte del commento
      if (commentToDelete) {
        const commentReplies = commentToDelete.replies || [];
        if (commentReplies.length > 0) {
          const replyDeleteActions = commentReplies.map(reply => ({
            type: 'delete' as const,
            replyId: reply.id,
            parentCommentId: commentId
          }));
          setPendingReplyActions(prev => [...prev, ...replyDeleteActions]);
          
          // Nascondi anche tutte le risposte dalla visualizzazione
          const replyIds = commentReplies.map(reply => reply.id);
          setRepliesWithPendingDelete(prev => new Set([...prev, ...replyIds]));
        }
      }
      
      // FIX 1: Rimuovi anche eventuali risposte pendenti a questo commento
      if (pendingReplies[commentId]) {
        setPendingReplies(prev => {
          const newPendingReplies = { ...prev };
          delete newPendingReplies[commentId];
          return newPendingReplies;
        });
        
        // Rimuovi anche le azioni pendenti delle risposte pendenti
        setPendingReplyActions(prev => 
          prev.filter(action => action.parentCommentId !== commentId)
        );
      }
      
      // FIX 2: Nascondi il commento dalla visualizzazione aggiungendolo al set
      setCommentsWithPendingDelete(prev => new Set([...prev, commentId]));
    } else {
      // Scenario 2: elimina immediatamente dal backend
      try {
        await deleteComment(commentId);
        console.log(`Commento ${commentId} eliminato automaticamente dal backend`);
      } catch (error) {
        console.error(`Errore nell'eliminazione automatica del commento ${commentId}:`, error);
      }
    }
  };

  // Funzione per validare che gli span evidenziati corrispondano ancora ai commenti esistenti
  const validateExistingHighlights = () => {
    if (!editorRef.current) return;

    const highlightedSpans = editorRef.current.querySelectorAll(`.${styles.highlighted}`);

    highlightedSpans.forEach((span) => {
      const refId = span.getAttribute('data-ref-id');

      if (refId) {
        // Verifica se esistono ancora commenti che si riferiscono a questo refId
        const existingComments = allComments.filter(comment => comment.refId === refId);

        // Verifica anche se lo span è ancora collegato al DOM dell'editor
        const spanStillInEditor = editorRef.current?.contains(span);

        // Rimuovi l'evidenziazione SOLO se:
        // 1. Nessun commento esiste più, OPPURE
        // 2. Lo span non è più nell'editor
        if (existingComments.length === 0 || !spanStillInEditor) {
          console.log(`Rimozione evidenziazione per refId: ${refId}, commenti esistenti: ${existingComments.length}, spanStillInEditor: ${spanStillInEditor}`);

          if (span.parentNode && spanStillInEditor) {
            // Sostituisci lo span con il suo contenuto testuale solo se è ancora nell'editor
            const textContent = span.textContent || '';
            const textNode = document.createTextNode(textContent);
            span.parentNode.replaceChild(textNode, span);
          }
        } else {
          // Aggiorna il tooltip con i commenti esistenti
          const tooltip = span.querySelector(`.${styles.tooltip}`);
          if (tooltip) {
            if (existingComments.length === 1) {
              tooltip.textContent = existingComments[0].text;
            } else {
              tooltip.textContent = `${existingComments.length} commenti su questo testo`;
            }
          }
        }
      }
    });
  };

  // Funzione per eliminare tutti i commenti quando l'editor viene svuotato
  const handleClearAllComments = async () => {
    console.log('Editor svuotato - eliminazione di tutti i commenti e risposte');
    
    if (canEditContent) {
      // Elimina tutti i commenti e risposte pendenti
      if (pendingComments.length > 0) {
        console.log(`Eliminazione di ${pendingComments.length} commenti pendenti`);
        setPendingComments([]);
        setPendingCommentActions([]);
      }
      
      const totalPendingReplies = Object.values(pendingReplies).reduce((sum, replies) => sum + replies.length, 0);
      if (totalPendingReplies > 0) {
        console.log(`Eliminazione di ${totalPendingReplies} risposte pendenti`);
        setPendingReplies({});
        setPendingReplyActions([]);
      }
      
      // Aggiungi alle azioni pendenti l'eliminazione di tutti i commenti del backend
      const backendComments = comments.filter(comment => !comment.id.startsWith('pending-'));
      if (backendComments.length > 0) {
        console.log(`Aggiunta eliminazione di ${backendComments.length} commenti del backend alle azioni pendenti`);
        const deleteActions = backendComments.map(comment => ({
          type: 'delete' as const,
          commentId: comment.id
        }));
        setPendingCommentActions(prev => [...prev, ...deleteActions]);
      }
    } else {
      // Scenario 2: elimina immediatamente tutti i commenti dal backend
      const allBackendComments = comments.filter(comment => !comment.id.startsWith('pending-'));
      if (allBackendComments.length > 0) {
        console.log(`Eliminazione immediata di ${allBackendComments.length} commenti dal backend`);
        
        for (const comment of allBackendComments) {
          try {
            await deleteComment(comment.id);
          } catch (error) {
            console.error(`Errore nell'eliminazione del commento ${comment.id}:`, error);
          }
        }
      }
    }
  };

  // Funzione per gestire l'input e prevenire che il testo finisca negli span evidenziati
  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    // Esegui prima le operazioni esistenti
    checkCurrentFormatting();
    
    // Controlla se l'editor è vuoto
    const content = e.currentTarget.textContent || '';
    const htmlContent = e.currentTarget.innerHTML || '';
    
    setIsEditorEmpty(content.trim() === '');
    
    // Controlla immediatamente se il contenuto è cambiato rispetto all'originale
    const currentContent = e.currentTarget.innerHTML || '';
    setHasContentChanges(currentContent !== originalContent);
    
    // Se l'editor è completamente vuoto, elimina tutti i commenti
    if (content.trim() === '' && htmlContent.trim() === '') {
      handleClearAllComments();
      return;
    }
    
    // Verifica se ci sono span evidenziati che potrebbero essere stati modificati
    // ma solo se non stiamo evidenziando
    if (!isHighlighting) {
      setTimeout(() => {
        preserveValidHighlights();
      }, 50);
    }
  };

  // Nuova funzione per preservare le evidenziazioni valide
  const preserveValidHighlights = () => {
    if (!editorRef.current) return;

    const highlightedSpans = editorRef.current.querySelectorAll(`.${styles.highlighted}`);

    highlightedSpans.forEach((span) => {
      const refId = span.getAttribute('data-ref-id');

      if (refId) {
        // Verifica se esistono ancora commenti che si riferiscono a questo refId
        const existingComments = allComments.filter(comment => comment.refId === refId);

        if (existingComments.length > 0) {
          const spanText = span.textContent || '';

          // Trova il primo commento esistente per verificare il testo
          const firstComment = existingComments[0];
          const originalCommentText = firstComment?.selection.content || '';

          // Se lo span contiene ancora il testo originale del commento (anche parzialmente),
          // mantieni l'evidenziazione
          if (spanText.includes(originalCommentText) || originalCommentText.includes(spanText)) {
            // L'evidenziazione è ancora valida, aggiorna il tooltip
            const tooltip = span.querySelector(`.${styles.tooltip}`);
            if (tooltip) {
              if (existingComments.length === 1) {
                tooltip.textContent = firstComment?.text || '';
              } else {
                tooltip.textContent = `${existingComments.length} commenti su questo testo`;
              }
            }
            return;
          }

          // Se il testo è completamente diverso, rimuovi l'evidenziazione
          if (spanText.trim() !== '' &&
              !originalCommentText.includes(spanText.trim()) &&
              !spanText.includes(originalCommentText)) {
            console.log(`Rimozione evidenziazione per testo modificato: "${spanText}" vs "${originalCommentText}"`);

            if (span.parentNode) {
              const textContent = span.textContent || '';
              const textNode = document.createTextNode(textContent);
              span.parentNode.replaceChild(textNode, span);
            }
          }
        } else {
          // Nessun commento esiste più, rimuovi l'evidenziazione
          if (span.parentNode) {
            const textContent = span.textContent || '';
            const textNode = document.createTextNode(textContent);
            span.parentNode.replaceChild(textNode, span);
          }
        }
      }
    });
  };

  // Funzione semplificata per gestire la digitazione
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    // Gestisce i tasti di cancellazione per verificare se del testo evidenziato viene rimosso
    if (e.key === 'Delete' || e.key === 'Backspace') {
      setTimeout(() => {
        validateExistingHighlights();
      }, 50);
    }
    
    // Gestisce Ctrl+A seguito da cancellazione
    if ((e.key === 'Delete' || e.key === 'Backspace') && 
        (e.ctrlKey || e.metaKey)) {
      setTimeout(() => {
        const content = editorRef.current?.textContent || '';
        if (content.trim() === '') {
          handleClearAllComments();
        }
      }, 50);
    }
    
    // Gestisce la digitazione all'interno di span evidenziati
    if (e.key.length === 1 || e.key === 'Enter') { // Caratteri stampabili o Enter
      handleTypingInHighlightedSpan();
    }
  };

  // Nuova funzione per gestire la digitazione all'interno di span evidenziati
  const handleTypingInHighlightedSpan = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let currentNode = range.startContainer;

    // Trova se il cursore è dentro uno span evidenziato
    let highlightedSpan: Element | null = null;
    let node: Node | null = currentNode;

    while (node && node !== editorRef.current) {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        if (element.classList.contains(styles.highlighted)) {
          highlightedSpan = element;
          break;
        }
      }
      node = node.parentNode;
    }

    if (highlightedSpan) {
      // Se stiamo digitando dentro uno span evidenziato, assicuriamoci che
      // il cursore sia posizionato correttamente e non nel tooltip
      const tooltip = highlightedSpan.querySelector(`.${styles.tooltip}`);
      
      if (tooltip && (currentNode === tooltip || tooltip.contains(currentNode))) {
        // Il cursore è nel tooltip, spostiamolo alla fine del testo principale
        const textNodes = Array.from(highlightedSpan.childNodes).filter(
          node => node.nodeType === Node.TEXT_NODE
        );
        
        if (textNodes.length > 0) {
          const lastTextNode = textNodes[textNodes.length - 1];
          const newRange = document.createRange();
          newRange.setStart(lastTextNode, lastTextNode.textContent?.length || 0);
          newRange.collapse(true);
          
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
      }
    }
  };

  // Funzione per gestire la navigazione con i tasti freccia negli span evidenziati
  const handleHighlightedSpanNavigation = (key: string) => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const currentNode = range.startContainer;

    // Trova se il cursore è dentro uno span evidenziato
    let highlightedSpan: Element | null = null;
    let node: Node | null = currentNode;

    while (node && node !== editorRef.current) {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        if (element.classList.contains(styles.highlighted)) {
          highlightedSpan = element;
          break;
        }
      }
      node = node.parentNode;
    }

    if (highlightedSpan) {
      const cursorOffset = range.startOffset;

      // Se stiamo andando a destra e siamo alla fine dello span
      if (key === 'ArrowRight' && 
          currentNode.nodeType === Node.TEXT_NODE && 
          currentNode.parentNode === highlightedSpan &&
          cursorOffset === (currentNode.textContent || '').length) {
        
        // Sposta il cursore dopo lo span
        const newRange = document.createRange();
        
        if (highlightedSpan.nextSibling) {
          if (highlightedSpan.nextSibling.nodeType === Node.TEXT_NODE) {
            newRange.setStart(highlightedSpan.nextSibling, 0);
          } else {
            newRange.setStartAfter(highlightedSpan);
          }
        } else {
          // Se non c'è un nodo dopo lo span, crea un nodo di testo vuoto
          const textNode = document.createTextNode('');
          highlightedSpan.parentNode?.insertBefore(textNode, highlightedSpan.nextSibling);
          newRange.setStart(textNode, 0);
        }
        
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }

      // Se stiamo andando a sinistra e siamo all'inizio dello span
      else if (key === 'ArrowLeft' && 
               currentNode.nodeType === Node.TEXT_NODE && 
               currentNode.parentNode === highlightedSpan &&
               cursorOffset === 0) {
        
        // Sposta il cursore prima dello span
        const newRange = document.createRange();
        
        if (highlightedSpan.previousSibling) {
          if (highlightedSpan.previousSibling.nodeType === Node.TEXT_NODE) {
            const prevText = highlightedSpan.previousSibling.textContent || '';
            newRange.setStart(highlightedSpan.previousSibling, prevText.length);
          } else {
            newRange.setStartBefore(highlightedSpan);
          }
        } else {
          // Se non c'è un nodo prima dello span, crea un nodo di testo vuoto
          const textNode = document.createTextNode('');
          highlightedSpan.parentNode?.insertBefore(textNode, highlightedSpan);
          newRange.setStart(textNode, 0);
        }
        
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    }
  };

  // Funzione per applicare le modifiche pendenti ai commenti per la visualizzazione
  const applyPendingModifications = (comment: Comment): Comment => {
    if (!canEditContent) return comment;

    // Controlla se ci sono modifiche pendenti per questo commento
    const pendingUpdate = pendingCommentActions.find(action => 
      action.type === 'update' && action.commentId === comment.id
    );

    let modifiedComment = { ...comment };

    // Applica la modifica al testo se presente
    if (pendingUpdate && pendingUpdate.data?.comment) {
      modifiedComment.text = pendingUpdate.data.comment;
    }

    // Applica le modifiche alle risposte
    if (comment.replies) {
      modifiedComment.replies = comment.replies
        .filter(reply => !repliesWithPendingDelete.has(reply.id)) // Filtra risposte con delete pendente
        .map(reply => {
          // Controlla se ci sono modifiche pendenti per questa risposta
          const pendingReplyUpdate = pendingReplyActions.find(action => 
            action.type === 'update' && action.replyId === reply.id
          );

          if (pendingReplyUpdate && pendingReplyUpdate.data?.comment) {
            return { ...reply, text: pendingReplyUpdate.data.comment };
          }

          return reply;
        });
    }

    return modifiedComment;
  };

  // Applica le modifiche pendenti ai commenti per la visualizzazione
  const displayComments = canEditContent 
    ? allComments.map(applyPendingModifications)
    : allComments;

  // Funzione per controllare se un commento ha modifiche pendenti
  const hasCommentPendingModifications = (commentId: string): boolean => {
    if (!canEditContent) return false;
    return pendingCommentActions.some(action => 
      (action.type === 'update' || action.type === 'delete') && action.commentId === commentId
    );
  };

  // Funzione per controllare se una risposta ha modifiche pendenti
  const hasReplyPendingModifications = (replyId: string): boolean => {
    if (!canEditContent) return false;
    return pendingReplyActions.some(action => 
      (action.type === 'update' || action.type === 'delete') && action.replyId === replyId
    );
  };

  // Funzione per verificare se ci sono modifiche pendenti
  const hasPendingChanges = (): boolean => {
    if (!canEditContent) return false;
    
    // Verifica modifiche ai commenti
    const hasCommentChanges = (
      pendingCommentActions.length > 0 ||
      pendingReplyActions.length > 0 ||
      pendingComments.length > 0 ||
      Object.keys(pendingReplies).length > 0 ||
      commentsWithPendingDelete.size > 0 ||
      repliesWithPendingDelete.size > 0
    );
    
    // Usa lo stato che viene aggiornato immediatamente durante la digitazione
    return hasCommentChanges || hasContentChanges;
  };

  // Funzione per mostrare il modal di conferma per l'annullamento
  const handleCancelChanges = () => {
    if (!canEditContent) return;
    setShowCancelConfirm(true);
  };

  // Funzione per eseguire l'annullamento dopo la conferma
  const confirmCancelChanges = async () => {
    try {
      // Pulisce tutti gli stati pendenti
      setPendingCommentActions([]);
      setPendingReplyActions([]);
      setPendingComments([]);
      setPendingReplies({});
      setCommentsWithPendingDelete(new Set());
      setRepliesWithPendingDelete(new Set());
      
      // Resetta anche lo stato delle modifiche al contenuto
      setHasContentChanges(false);

      // Ripristina il contenuto originale dello sheet
      if (editorRef.current) {
        editorRef.current.innerHTML = originalContent;
        setIsEditorEmpty(originalContent.trim() === '');
        
        // Rimuovi tutte le evidenziazioni esistenti per evitare duplicazioni
        const highlightedSpans = editorRef.current.querySelectorAll(`.${styles.highlighted}`);
        highlightedSpans.forEach(span => {
          if (span.parentNode) {
            const textContent = span.textContent || '';
            const textNode = document.createTextNode(textContent);
            span.parentNode.replaceChild(textNode, span);
          }
        });
      }

      // Ricarica i commenti dal backend per ripristinare lo stato originale
      if (loadComments) {
        await loadComments();
        
                 // Dopo aver ricaricato i commenti, ricrea le evidenziazioni per i commenti esistenti
         setTimeout(() => {
           if (editorRef.current && backendComments.length > 0) {
             backendComments.forEach(backendComment => {
               // Converte il commento backend nel formato locale per l'evidenziazione
               const localComment = convertToLocalComment(backendComment);
               
               // Verifica se il testo del commento esiste ancora nel contenuto
               const editorContent = editorRef.current!.textContent || '';
               if (editorContent.includes(localComment.selection.content)) {
                 // Cerca e seleziona il testo del commento per poterlo evidenziare
                 const searchText = localComment.selection.content;
                 const textIndex = editorContent.indexOf(searchText);
                 
                 if (textIndex !== -1) {
                   // Crea una selezione temporanea per il testo del commento
                   const range = document.createRange();
                   const walker = document.createTreeWalker(
                     editorRef.current!,
                     NodeFilter.SHOW_TEXT,
                     null
                   );

                   let currentIndex = 0;
                   let found = false;

                   while (walker.nextNode() && !found) {
                     const node = walker.currentNode as Text;
                     const nodeText = node.textContent || '';
                     const nodeLength = nodeText.length;

                     if (currentIndex + nodeLength > textIndex) {
                       const startOffset = textIndex - currentIndex;
                       const endOffset = Math.min(startOffset + searchText.length, nodeLength);
                       
                       try {
                         range.setStart(node, startOffset);
                         range.setEnd(node, endOffset);
                         
                         // Salva temporaneamente il range come se fosse una selezione
                         selectionRef.current = range.cloneRange();
                         
                         // Usa la funzione esistente per creare l'evidenziazione
                         highlightCommentedText(localComment);
                         
                         found = true;
                       } catch (error) {
                         console.error(`Errore nel ricreare l'evidenziazione per il commento ${localComment.id}:`, error);
                       }
                     }
                     
                     currentIndex += nodeLength;
                   }
                 }
               }
             });
           }
         }, 100); // Piccolo delay per assicurarsi che il contenuto sia stato ripristinato
      }

      console.log('Tutte le modifiche pendenti sono state annullate');
    } catch (error) {
      console.error('Errore nell\'annullare le modifiche:', error);
      alert('Errore nell\'annullare le modifiche. Riprova.');
    }
  };

  return (
    <div className={styles.textEditorContainer} ref={containerRef}>
      {/* Toolbar - visibile solo quando si può editare il contenuto */}
      {canEditContent && (
        <div className={styles.toolbar}>
          <div className={styles.toolbarGroup}>
            <button
              className={isBold ? styles.toolbarButtonActive : styles.toolbarButton}
              onClick={() => execCommand('bold')}
              aria-label="Grassetto"
            >
              <HiBold />
            </button>
            <button
              className={isItalic ? styles.toolbarButtonActive : styles.toolbarButton}
              onClick={() => execCommand('italic')}
              aria-label="Corsivo"
            >
              <span style={{ fontStyle: 'italic' }}>I</span>
            </button>
            <button
              className={isUnderline ? styles.toolbarButtonActive : styles.toolbarButton}
              onClick={() => execCommand('underline')}
              aria-label="Sottolineato"
            >
              <span style={{ textDecoration: 'underline' }}>U</span>
            </button>
          </div>

          <div className={styles.toolbarGroup}>
            <button
              className={isList ? styles.toolbarButtonActive : styles.toolbarButton}
              onClick={() => execCommand('insertUnorderedList')}
              aria-label="Lista puntata"
            >
              <HiListBullet />
            </button>
            <button
              className={styles.toolbarButton}
              onClick={openTableModal}
              aria-label="Inserisci tabella"
            >
              <HiTable />
            </button>
            <button
              className={styles.toolbarButton}
              onClick={insertLink}
              aria-label="Inserisci link"
            >
              <HiLink />
            </button>
          </div>

          <div className={styles.toolbarGroup}>
            <div className={styles.colorPickerWrapper}>
              <button
                className={styles.toolbarButton}
                onClick={openColorPicker}
                aria-label="Colore testo"
              >
                <HiColorSwatch />
              </button>
              <ColorPicker
                isOpen={isColorPickerOpen}
                onClose={() => setIsColorPickerOpen(false)}
                onSelectColor={changeTextColor}
              />
            </div>
            <div className={styles.fontSizeSelector}>
              <select
                onChange={(e) => changeFontSize(e.target.value)}
                className={styles.fontSizeSelect}
                value={currentFontSize}
              >
                <option value="1">Piccolo</option>
                <option value="3">Normale</option>
                <option value="5">Grande</option>
                <option value="7">Molto grande</option>
              </select>
            </div>
          </div>

          <div className={styles.toolbarSpacer}></div>

          <button
            className={styles.toolbarButtonWithText}
            onClick={handleCancelChanges}
            aria-label="Annulla modifiche"
            title="Annulla tutte le modifiche pendenti"
            disabled={!hasPendingChanges()}
          >
            <HiTrash />
            <span>Annulla</span>
          </button>
          <button
            className={styles.toolbarButtonWithText}
            onClick={handleSaveSheet}
            aria-label="Salva sheet"
            title="Salva sheet e commenti"
          >
            <HiOutlineSave />
            <span>Salva</span>
          </button>
        </div>
      )}

      {/* Icona fluttuante per aggiungere commenti */}
      {commentIconPosition && currentSelection && canComment && (
        <div
          className={styles.commentIcon}
          style={{
            top: `${commentIconPosition.top}px`,
            left: `${commentIconPosition.left}px`
          }}
          onClick={handleAddComment}
        >
          <HiOutlineChatAlt size={20} />
        </div>
      )}

      <div className={styles.editorArea}>
        <div
          className={styles.editor}
          contentEditable={canEditContent}
          ref={editorRef}
          onInput={canEditContent ? handleInput : undefined}
          onMouseUp={handleTextSelection}
          onKeyDown={canEditContent ? handleKeyDown : undefined}
          onKeyUp={(e) => {
            // Gestisci la selezione del testo dopo aver rilasciato i tasti
            handleTextSelection();
            
            // Gestisce i tasti freccia per uscire dagli span evidenziati solo se si può editare
            if (canEditContent && ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
              setTimeout(() => {
                handleHighlightedSpanNavigation(e.key);
              }, 0);
            }
          }}
          onFocus={() => setIsEditorFocused(true)}
          onBlur={() => setIsEditorFocused(false)}
          onClick={() => {
            // Aspettiamo un momento per vedere se l'utente sta selezionando del testo
            setTimeout(() => {
              const selection = window.getSelection();
              // Cancelliamo la selezione solo se non c'è testo selezionato
              if (!selection || selection.toString().length === 0) {
                clearSelection();
              }
            }, 10);
          }}
          onPaste={canEditContent ? (e) => {
            // Preveniamo il comportamento di default
            e.preventDefault();

            // Otteniamo il testo puro dalla clipboard
            const text = e.clipboardData.getData('text/plain');

            // Inseriamo il testo puro nel punto di inserimento
            document.execCommand('insertText', false, text);

            // Aggiorna lo stato dell'editor vuoto
            setIsEditorEmpty(false);
          } : undefined}
          suppressContentEditableWarning={true}
        >
        </div>
        {/* Placeholder visibile solo quando si può editare il contenuto */}
        {canEditContent && isEditorEmpty && !isEditorFocused && (
          <div className={styles.editorPlaceholder}>
            Inizia a scrivere qui...
          </div>
        )}

        <div
          className={styles.commentsList}
          style={{ width: `${commentsWidth}px` }}
          ref={commentsListRef}
        >
          <div
            className={`${styles.resizeHandle} ${isDragging ? styles.dragging : ''}`}
            ref={resizeHandleRef}
            onMouseDown={() => setIsDragging(true)}
          ></div>
          <h3>Commenti</h3>
          
          {/* Gestione errori */}
          {commentsError && (
            <div className={styles.errorMessage}>
              <p>Errore: {commentsError}</p>
              <button onClick={clearError} className={styles.retryButton}>
                Riprova
              </button>
            </div>
          )}
          
          {/* Loading */}
          {commentsLoading && (
            <div className={styles.loadingMessage}>
              Caricamento commenti...
            </div>
          )}
          
          {/* Lista commenti */}
          {!commentsLoading && displayComments.length === 0 ? (
            <p className={styles.noComments}>Nessun commento</p>
          ) : !commentsLoading && (
            <ul>
              {displayComments.map(comment => (
                <li
                  key={comment.id}
                  data-comment-id={comment.id}
                  className={`${styles.commentItem} ${highlightedCommentId === comment.id ? styles.commentItemHighlighted : ''}`}
                  onMouseEnter={() => handleCommentHover(comment.id, 'enter')}
                  onMouseLeave={() => handleCommentHover(comment.id, 'leave')}
                >
                  <div className={styles.commentHeader}>
                    <div className={styles.commentAuthor}>{comment.author}</div>
                    <div className={styles.commentDate}>
                      {comment.createdAt.toLocaleDateString()} {comment.createdAt.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      {/* Indicatore per commenti pendenti */}
                      {canEditContent && comment.id.startsWith('pending-') && (
                        <span className={styles.pendingIndicator} title="Commento in attesa di salvataggio">
                          {' '}🕐
                        </span>
                      )}
                      {/* Indicatore per commenti con modifiche pendenti */}
                      {canEditContent && !comment.id.startsWith('pending-') && hasCommentPendingModifications(comment.id) && (
                        <span className={styles.pendingIndicator} title="Commento con modifiche in attesa di salvataggio">
                          {' '}✏️
                        </span>
                      )}
                    </div>
                  </div>

                  <div className={styles.commentText}>{comment.text}</div>

                  {canComment && <div className={styles.commentActions}>
                    <button
                      className={styles.commentActionIcon}
                      onClick={() => setReplyToCommentId(replyToCommentId === comment.id ? null : comment.id)}
                      aria-label="Rispondi al commento"
                    >
                      <HiReply size={16} />
                    </button>
                    {comment.authorId && comment.authorId === user?.id && (
                      <>
                        <button
                          className={styles.commentActionIcon}
                          onClick={() => {
                            setCommentToEdit(comment);
                            setIsEditModalOpen(true);
                          }}
                          aria-label="Modifica commento"
                        >
                          <HiPencil size={16} />
                        </button>
                        <button
                          className={`${styles.commentActionIcon} ${styles.commentActionIconDelete}`}
                          onClick={() => handleDeleteComment(comment.id)}
                          aria-label="Elimina commento"
                        >
                          <HiTrash size={16} />
                        </button>
                      </>
                    )}
                  </div> }

                  {replyToCommentId === comment.id && (
                    <div className={styles.replyForm}>
                      <textarea
                        ref={replyTextareaRef}
                        className={styles.replyInput}
                        placeholder="Scrivi una risposta..."
                        value={replyText}
                        onChange={(e) => setReplyText(e.target.value)}
                      />
                      <div className={styles.replyFormActions}>
                        <Button
                          variant="regular"
                          type="secondary"
                          onClick={() => {
                            setReplyToCommentId(null);
                            setReplyText('');
                          }}
                        >
                          Annulla
                        </Button>
                        <Button
                          variant="regular"
                          type="primary"
                          onClick={() => handleAddReply(comment.id)}
                          disabled={replyText.trim() === ''}
                        >
                          Invia
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Sezione unificata per tutte le risposte (backend + pendenti con modifiche applicate) */}
                  {(() => {
                    const allReplies = getAllRepliesForComment(comment.id);
                    return allReplies.length > 0 && (
                      <div className={styles.replies}>
                        {allReplies.map(reply => (
                          <div key={reply.id} className={styles.reply}>
                            <div className={styles.commentHeader}>
                              <div className={styles.commentAuthor}>{reply.author}</div>
                              <div className={styles.commentDate}>
                                {reply.createdAt.toLocaleDateString()} {reply.createdAt.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                                {/* Indicatore per risposte pendenti */}
                                {canEditContent && reply.id.startsWith('pending-reply-') && (
                                  <span className={styles.pendingIndicator} title="Risposta in attesa di salvataggio">
                                    {' '}🕐
                                  </span>
                                )}
                                {/* Indicatore per risposte con modifiche pendenti */}
                                {canEditContent && !reply.id.startsWith('pending-reply-') && hasReplyPendingModifications(reply.id) && (
                                  <span className={styles.pendingIndicator} title="Risposta con modifiche in attesa di salvataggio">
                                    {' '}✏️
                                  </span>
                                )}
                              </div>
                            </div>
                            {canComment && <div className={styles.replyActions}>
                              {reply.authorId && reply.authorId === user?.id && (
                                <>
                                  <button
                                    className={styles.commentActionIcon}
                                    onClick={() => {
                                      setReplyToEdit({commentId: comment.id, replyId: reply.id});
                                      setEditReplyText(reply.text);
                                    }}
                                    aria-label="Modifica risposta"
                                  >
                                    <HiPencil size={16} />
                                  </button>
                                  <button
                                    className={`${styles.commentActionIcon} ${styles.commentActionIconDelete}`}
                                    onClick={() => handleDeleteReply(comment.id, reply.id)}
                                    aria-label="Elimina risposta"
                                  >
                                    <HiTrash size={16} />
                                  </button>
                                </>
                              )}
                            </div>}
                            {replyToEdit && replyToEdit.replyId === reply.id ? (
                              <div className={styles.replyForm}>
                                <textarea
                                  className={styles.replyInput}
                                  placeholder="Modifica la tua risposta..."
                                  value={editReplyText}
                                  onChange={(e) => setEditReplyText(e.target.value)}
                                  autoFocus
                                />
                                <div className={styles.replyFormActions}>
                                  <Button
                                    variant="regular"
                                    type="secondary"
                                    onClick={() => {
                                      setReplyToEdit(null);
                                      setEditReplyText('');
                                    }}
                                  >
                                    Annulla
                                  </Button>
                                  <Button
                                    variant="regular"
                                    type="primary"
                                    onClick={() => handleEditReply(comment.id, reply.id, editReplyText)}
                                    disabled={editReplyText.trim() === ''}
                                  >
                                    Salva
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className={styles.commentText}>{reply.text}</div>
                            )}
                          </div>
                        ))}
                      </div>
                    );
                  })()}
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      <CommentModal
        isOpen={isCommentModalOpen}
        onClose={() => setIsCommentModalOpen(false)}
        onSubmit={handleSaveCommentWithLogic}
        selectedText={currentSelection?.content || ''}
      />

      {commentToEdit && (
        <EditCommentModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setCommentToEdit(null);
          }}
          onSubmit={(newText) => {
            handleEditComment(commentToEdit.id, newText);
            setIsEditModalOpen(false);
            setCommentToEdit(null);
          }}
          onDelete={() => {
            handleDeleteComment(commentToEdit.id);
            setIsEditModalOpen(false);
            setCommentToEdit(null);
          }}
          commentText={commentToEdit.text}
          selectedText={commentToEdit.selection.content}
        />
      )}

      {/* Modal per inserire una tabella */}
      <TableModal
        isOpen={isTableModalOpen}
        onClose={() => setIsTableModalOpen(false)}
        onInsert={insertTable}
      />

      {/* Modal di conferma per l'annullamento delle modifiche */}
      <ConfirmDialog
        isOpen={showCancelConfirm}
        onClose={() => setShowCancelConfirm(false)}
        onConfirm={confirmCancelChanges}
        title="Annulla modifiche"
        message="Sei sicuro di voler annullare tutte le modifiche pendenti? Questa azione non può essere annullata."
        confirmLabel="Annulla modifiche"
        cancelLabel="Mantieni modifiche"
        confirmType="primary"
      />

      {/* Il modale per modificare le risposte è stato rimosso, ora la modifica avviene inline */}
    </div>
  );
};
